<html>
    <head>
        <title>7-3-3</title>
    </head>

    <body>
        <h1>Student Search System</h1>
        <form method=get action="<?php echo $_SERVER['PHP_SELF']; ?>">
            <h2>Choose Search Type:<br/></h2>
            <select name=searchtype>
                <option value="id">id</option>
                <option value="firstname">firstname</option>
                <option value="lastname">lastname</option>
                <option value="email">email</option>
                <option value="age">age (>=)</option>
            </select>
            <h2>Enter Search Term:</h2>
            <input name=searchterm type=text size=40>
            <span style="color:red;">(Enter an * to list all students)</span>
            <br/>
            <input type=submit name=submit value=Search>
        </form>
        <hr>

        <?php
            require_once("My-DB-Functions.php");

            if (isset($_REQUEST["submit"]))
            {
                if (isset($_REQUEST["searchterm"]))
                {
                    $conn = My_Connect_DB();

                        if ($_REQUEST["searchterm"] == "*")
                        {
                            $sql = "SELECT * FROM Students;";
                        }
                        else if ($_REQUEST['searchtype'] == "age")
                        {
                            $sql = "SELECT * FROM Students WHERE ".$_REQUEST['searchtype'].">='".$_REQUEST["searchterm"]."';";
                        }
                        else
                        {
                            $sql = "SELECT * FROM Students WHERE ".$_REQUEST['searchtype']."='".$_REQUEST["searchterm"]."';";
                        }
                        Run_Select_Show_Results($conn, $sql);

                    My_Disconnect_DB($conn);
                }
            }
        ?>
    </body>
</html>