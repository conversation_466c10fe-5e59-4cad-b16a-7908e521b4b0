<?php
    function My_Connect_DB()
    {
        $server = "localhost";
        $user = "mvazquez_matvazguz";
        $password = "?_SPl^R*^9.F";
        $dbname = "mvazquez_itec4450";

        $conn = mysqli_connect($server, $user, $password, $dbname);

        if (!$conn)
            die("Connection to Database failed: ".mysqli_connect_error()."<br>");
        else return $conn;
    }

    function My_SQL_EXE($conn, $sql)
    {
        $result = mysqli_query($conn, $sql);

        if ($result)
            echo "SQL is done successfully! <br>";
        else
            echo "Error in running sql: ".$sql." with error: ".mysqli_error($conn)."<br>";
        return $result;
    }

    function My_Disconnect_DB($conn)
    {
        mysqli_close($conn);
    }

    function Run_Select_Show_Results($conn, $sql) // here the sql statement must be a SELECT sql statement
    {
        $result = My_SQL_EXE($conn, $sql);

        echo "<table border='1'>";
            echo "<tr>";
                while ($fieldinfo = mysqli_fetch_field($result))
                {
                    echo "<th>";
                        echo $fieldinfo->name;
                    echo "</th>";
                }
            echo "</tr>";

            while ($row = mysqli_fetch_assoc($result))
            {
                echo "<tr>";
                    foreach ($row as $key=>$value)
                        echo "<td>".$value."</td>";
                echo "</tr>";
            }
        echo "</table>";
        echo "Total rows: ".mysqli_num_rows($result).'<br>';
    }

    function Run_SQL_Show_Result($conn, $sql, $table)
    {
        My_SQL_EXE($conn, $sql);
        $sql = "SELECT * FROM ".$table.";";
        Run_Select_Show_Results($conn, $sql);
    }
?>