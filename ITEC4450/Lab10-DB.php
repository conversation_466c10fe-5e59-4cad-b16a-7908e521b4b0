<?php
    function My_Connect_DB()
    {
        $server = "localhost";
        $user = "mvazquez_matvazguz";
        $password = "?_SPl^R*^9.F";
        $dbname = "mvazquez_itec4450";

        $conn = mysqli_connect($server, $user, $password, $dbname);

        if (!$conn)
            die("Connection to Database failed: ".mysqli_connect_error()."<br>");
        else return $conn;
    }

    function My_SQL_EXE($conn, $sql)
    {
        $result = mysqli_query($conn, $sql);

        if ($result)
            echo "SQL is done successfully! <br>";
        else
            echo "Error in running sql: ".$sql." with error: ".mysqli_error($conn)."<br>";
        return $result;
    }

    function My_Disconnect_DB($conn)
    {
        mysqli_close($conn);
    }

    function Run_Select_Show_Results($conn, $sql)
    {
        $result = My_SQL_EXE($conn, $sql);

        echo "<table border='1'>";
        echo "<tr>";
        while ($fieldinfo = mysqli_fetch_field($result))
        {
            echo "<th>";
            echo $fieldinfo->name;
            echo "</th>";
        }
        echo "</tr>";

        while ($row = mysqli_fetch_assoc($result))
        {
            echo "<tr>";
            foreach ($row as $key=>$value)
                echo "<td>".$value."</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "Total rows: ".mysqli_num_rows($result).'<br>';
    }

    function Run_SQL_Show_Result($conn, $sql, $table)
    {
        My_SQL_EXE($conn, $sql);
        $sql = "SELECT * FROM ".$table.";";
        Run_Select_Show_Results($conn, $sql);
    }
?>

<?php
    $conn = My_Connect_DB();

    $sql = "CREATE TABLE Products(
                                    id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                                    name VARCHAR(30) NOT NULL,
                                    maker VARCHAR(30),
                                    amount int(4),
                                    price float(2)
                                    );";

    My_SQL_EXE($conn, $sql);

    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM Products");

    if ($result) {
        $row = mysqli_fetch_assoc($result);
        if ($row['count'] > 0) {
            echo "Table already has data. Skipping insertion.<br>";
        } else {
            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('iPhone 14', 'Apple', '50', '999.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Galaxy S23', 'Samsung', '30', '899.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('MacBook Pro', 'Apple', '15', '1999.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Surface Laptop', 'Microsoft', '20', '1299.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('ThinkPad X1', 'Lenovo', '25', '1599.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('iPad Air', 'Apple', '40', '599.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Galaxy Buds', 'Samsung', '80', '149.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('AirPods Pro', 'Apple', '100', '249.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Echo Dot', 'Amazon', '200', '49.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Kindle', 'Amazon', '150', '89.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Xbox Series X', 'Microsoft', '10', '499.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Fire TV Stick', 'Amazon', '300', '39.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('PlayStation 5', 'Sony', '8', '499.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Nintendo Switch', 'Nintendo', '45', '299.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Apple Watch', 'Apple', '60', '399.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Galaxy Watch', 'Samsung', '40', '329.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Surface Pro', 'Microsoft', '18', '1099.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('iMac', 'Apple', '12', '1299.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Dell XPS 13', 'Dell', '22', '999.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('HP Spectre', 'HP', '28', '1199.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Asus ROG', 'Asus', '15', '1799.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Google Pixel', 'Google', '35', '699.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Chromebook', 'Google', '50', '299.99');";
            My_SQL_EXE($conn, $sql);

            $sql = "INSERT INTO Products (name, maker, amount, price)
                    VALUES('Galaxy Tab', 'Samsung', '35', '449.99');";
            My_SQL_EXE($conn, $sql);
        }
    }

    My_Disconnect_DB($conn);
?>