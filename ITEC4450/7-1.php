<?php
    function My_Connect_DB()
    {
        $server = "localhost";
        $user = "mvazquez_matvazguz";
        $password = "?_SPl^R*^9.F";
        $dbname = "mvazquez_itec4450";

        $conn = mysqli_connect($server, $user, $password, $dbname);

        if (!$conn)
            die("Connection to Database failed: ".mysqli_connect_error()."<br>");
        else return $conn;
    }

    function My_SQL_EXE($conn, $sql)
    {
        $result = mysqli_query($conn, $sql);

        if ($result)
            echo "SQL is done successfully! <br>";
        else
            echo "Error in running sql: ".$sql." with error: ".mysqli_error($conn)."<br>";
        return $result;
    }

    function My_Disconnect_DB($conn)
    {
        mysqli_close($conn);
    }
?>

<?php
    $conn = My_Connect_DB();
    $sql = "CREATE TABLE Students(
                                    id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                                    firstname VARCHAR(30) NOT NULL,
                                    lastname VARCHAR(30) NOT NULL,
                                    email VARCHAR(50) unique,
                                    reg_date TIMESTAMP
                                    );";

    $result = My_SQL_EXE($conn, "SHOW TABLES;");

    if (!$result)
        My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Micheal', 'Jordan', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Matilda', 'Vazquez', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Michelle', 'Vazquez', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Matilda', 'Michelle', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Clark', 'Kent', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Bruce', 'Wayne', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('John', 'Wick', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Harry', 'Potter', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Olivia', 'Rodrigo', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Dana', 'Scully', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
            VALUES('Johnny', 'Suh', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    $sql = "INSERT INTO Students (firstname, lastname, email) 
                VALUES('Mark', 'Lee', '<EMAIL>');";
    My_SQL_EXE($conn, $sql);

    My_Disconnect_DB($conn);
?>