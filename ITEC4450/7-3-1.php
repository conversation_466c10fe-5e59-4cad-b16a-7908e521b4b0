<html>
    <head>
        <title>7-3-1</title>
    </head>

    <body>
        <h1>Please Sign up first!</h1>
        <form method=get action="<?php echo $_SERVER['PHP_SELF']; ?>">
            First name: <input type=text name=first><br/><br/>
            Last name: <input type=text name=last><br/><br/>
            Email: <input type=text name=email><br/><br/>
            <input type=submit name=insert value="Sign me up"><br/>
            <input type=submit name=remove value="Remove me"><br/>
            <input type=submit name=show value="Display all"><br/>
        </form>

        <?php
            require_once("My-DB-Functions.php");

            $conn = My_Connect_DB();

            if (isset($_REQUEST["insert"]))
            {
                $sql = "INSERT INTO Students(firstname, lastname, email) 
                        VALUES('".$_REQUEST["first"]."', '".$_REQUEST["last"]."', '".$_REQUEST["email"]."');";
                Run_SQL_Show_Result($conn, $sql, "Students");
            }
            else if (isset($_REQUEST["remove"]))
            {
                $sql = "DELETE FROM Students WHERE firstname='".$_REQUEST["first"]."' AND lastname='".$_REQUEST["last"]."' AND email='".$_REQUEST["email"]."';";
                Run_SQL_Show_Result($conn, $sql, "Students");
            }
            else if (isset($_REQUEST["show"]))
            {
                $sql = "SELECT * FROM Students;";
                Run_Select_Show_Results($conn, $sql);
            }

            My_Disconnect_DB($conn);
        ?>
    </body>
</html>