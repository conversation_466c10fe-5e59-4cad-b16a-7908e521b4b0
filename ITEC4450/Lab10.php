<html>
    <head>
        <title>Lab 10</title>
    </head>

    <body>
        <h1>Please provide the information about your product:</h1>
        <form action="" method="get">
            Product's name:     <input type="text" name="name"> <font color=red>*</font><br/><br/>
            Product's maker:    <input type="text" name="maker"><br/><br/>
            Product's amount:   <input type="text" name="amount"><br/><br/>
            Product's price:    <input type="text" name="price"><br/><br/>

            <input type="submit" value="Add Record" name="add">
            <input type="submit" value="Find the Item with the Highest Price" name="highest">
            <input type="submit" value="Display All" name="displayAll"> <br/><br/>
            <input type="submit" value="Group by Maker" name="groupByMaker">
            <input type="submit" value="Sort Items by Amount Descendly" name="sort">
            <input type="submit" value="Find the Average Price of all items" name="average"><br/><br/>
            <input type="submit" value="Delete Record by ID" name="delete"> ID to remove: <input type="text" name="id"><br/><br/>
            <input type="submit" value="Show by Maker" name="showByMaker"> Maker: <input type="text" name="maker2show"><br/><br/>
        </form>
        <hr/>

        <?php
            require_once("My-DB-Functions.php");

            $conn = My_Connect_DB();

            if (isset($_REQUEST["add"]))
            {
                if (!empty($_REQUEST["name"]))
                {
                    $name = $_REQUEST["name"];
                    $maker = $_REQUEST["maker"];
                    $amount = $_REQUEST["amount"];
                    $price = $_REQUEST["price"];

                    $sql = "INSERT INTO Products(name, maker, amount, price)
                            VALUES('".$name."', '".$maker."', ".$amount.", ".$price.");";
                    Run_SQL_Show_Result($conn, $sql, "Products");
                }
                else
                {
                    echo "<p style='color:red;'>Product name is required!</p>";
                }
            }
            else if (isset($_REQUEST["highest"]))
            {
                $sql = "SELECT * FROM Products WHERE price = (SELECT MAX(price) FROM Products);";
                echo "<h3>Item with the Highest Price:</h3>";
                Run_Select_Show_Results($conn, $sql);
            }
            else if (isset($_REQUEST["displayAll"]))
            {
                $sql = "SELECT * FROM Products;";
                echo "<h3>All Products:</h3>";
                Run_Select_Show_Results($conn, $sql);
            }
            else if (isset($_REQUEST["groupByMaker"]))
            {
                $sql = "SELECT maker, COUNT(*) as count, AVG(price) as avg_price FROM Products GROUP BY maker;";
                echo "<h3>Products Grouped by Maker:</h3>";
                Run_Select_Show_Results($conn, $sql);
            }
            else if (isset($_REQUEST["sort"]))
            {
                $sql = "SELECT * FROM Products ORDER BY amount DESC;";
                echo "<h3>Items Sorted by Amount (Descending):</h3>";
                Run_Select_Show_Results($conn, $sql);
            }
            else if (isset($_REQUEST["average"]))
            {
                $sql = "SELECT AVG(price) as average_price FROM Products;";
                echo "<h3>Average Price of All Items:</h3>";
                Run_Select_Show_Results($conn, $sql);
            }
            else if (isset($_REQUEST["delete"]))
            {
                if (!empty($_REQUEST["id"]))
                {
                    $id = $_REQUEST["id"];
                    $sql = "DELETE FROM Products WHERE id=".$id.";";
                    Run_SQL_Show_Result($conn, $sql, "Products");
                }
                else
                {
                    echo "<p style='color:red;'>Please enter an ID to delete!</p>";
                }
            }
            else if (isset($_REQUEST["showByMaker"]))
            {
                if (!empty($_REQUEST["maker2show"]))
                {
                    $maker = $_REQUEST["maker2show"];
                    $sql = "SELECT * FROM Products WHERE maker='".$maker."';";
                    echo "<h3>Products by ".$maker.":</h3>";
                    Run_Select_Show_Results($conn, $sql);
                }
                else
                {
                    echo "<p style='color:red;'>Please enter a maker name!</p>";
                }
            }

            My_Disconnect_DB($conn);
        ?>

    </body>
</html>