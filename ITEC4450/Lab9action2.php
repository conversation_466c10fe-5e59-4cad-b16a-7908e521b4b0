<?php
    session_start();
?>

<html>
    <head>
        <title>Lab 9</title>
    </head>

    <body>
        <?php
            if (!isset($_SESSION["buy"]) || empty($_SESSION["buy"]) || !isset($_SESSION["totalCost"]) || $_SESSION["totalCost"] <= 0)
            {
                echo "<h1>Your cart is empty!</h1>";
                echo "Please add items to your cart before checking out.<br>";
                echo "<a href='Lab9.php'>Go Back to Store</a>";
                exit();
            }
        ?>

        <h1>Thank you for shopping with us. Please pay $<?php echo $_SESSION["totalCost"]; ?></h1>

        <form method="post" action="Lab9action3.php">
            Please provide your shipping address:   <input type="text" name="address" required> <br>
            Please provide your credit card number: <input type="text" name="card" required> <br>

            <br>
            <input type="submit" name="submit" value="Pay now">
            <input type="reset">
        </form>
    </body>
</html>