<?php
    session_start();
?>

<html>
    <head>
        <title>Project 3</title>
    </head>

    <body>
        <?php
            if (isset($_POST["login"]))
            {
                $user = $_POST["user"];
                $passwd = $_POST["passwd"];

                if ($user == "admin" && $passwd == "admin")
                {
                    $_SESSION["user"] = $user;
                    $_SESSION["logged_in"] = true;
                }
                else
                {
                    echo "Invalid username or password! Please try again. <br>";
                    echo "<a href='Project3.php'>Back to Login</a>";
                    exit();
                }
            }

            $user = $_SESSION["user"];

            // once form is submitted it welcome div section should be replaced with the form radio handlers
            echo "<div style='background-color:pink; border:red solid 1px; width:75%; margin:auto;'>";
            echo "Welcome ".$user."<br>";
            date_default_timezone_set("America/New_York");
            $now = time();
            echo "Now is ".date("F j, Y", $now)." ".date("h:i:s A", $now)." <br>";
            echo "</div>";
            echo "<hr>";

            echo "<div style='background-color:lightblue; border:red solid 1px; width:75%; margin:auto;'>";
            echo "Choose what you want to do based on the following menu: <br>";
            echo "<hr>";

            echo "<form method='post' action='".$_SERVER['PHP_SELF']."'>";
            echo "<input type='radio' name='showBalance'> Show Balance <br>";
            echo "<input type='radio' name='deposit'>     Deposit this amount: <input type='text' name=''> <br>";
            echo "<input type='radio' name='withdraw'>    Withdraw this amount: <input type='text' name=''> <br>";
            echo "<input type='radio' name='tranAction'>  Show my transactions <br>";
            echo "<input type='radio' name='changePass'>  Change my password <br>";
            echo "<input type='radio' name='logout'>      Log Out <br>";
            echo "<input type='submit' name='submit' value='Submit'>";
            echo "</form>";
            echo "<hr>";
            echo "</div>";
        ?>
    </body>
</html>