<?php
    session_start();
?>

<html>
    <head>
        <title>Project 3</title>
    </head>

    <body>
        <?php
            if (isset($_POST["login"]))
            {
                $user = $_POST["user"];
                $passwd = $_POST["passwd"];

                if ($user == "admin" && $passwd == "admin")
                {
                    $_SESSION["user"] = $user;
                    $_SESSION["logged_in"] = true;
                    $_SESSION["password"] = $passwd; // store the current password

                    if (!isset($_SESSION["balance"]))
                    {
                        $_SESSION["balance"] = 0;
                    }
                    if (!isset($_SESSION["transactions"]))
                    {
                        $_SESSION["transactions"] = array();
                    }
                }
                else
                {
                    echo "Invalid username or password! Please try again. <br>";
                    echo "<a href='Project3.php'>Back to Login</a>";
                    exit();
                }
            }
            /*
            if (!isset($_SESSION["logged_in"]) || $_SESSION["logged_in"] != true)
            {
                echo "You must be logged in to access this page. <br>";
                echo "<a href='Project3.php'>Back to Login</a>";
                exit();
            }
            */

            $user = $_SESSION["user"];
            $message = "";

            if (isset($_POST["submit"]))
            {
                // Show Balance
                if (isset($_POST["showBalance"]))
                {
                    $message = "Your current balance is: $" . number_format($_SESSION["balance"], 2);
                }
                // Deposit
                elseif (isset($_POST["deposit"]))
                {
                    $amount = floatval($_POST["depositAmount"]);
                    if ($amount > 0) {
                        $_SESSION["balance"] += $amount;
                        $transaction = date("Y-m-d H:i:s") . " - Deposit: $" . number_format($amount, 2);
                        $_SESSION["transactions"][] = $transaction;
                        $message = "Successfully deposited $" . number_format($amount, 2) . ". New balance: $" . number_format($_SESSION["balance"], 2);
                    } else {
                        $message = "Deposit amount must be greater than $0.00";
                    }
                }
                // Withdraw
                elseif (isset($_POST["withdraw"])) {
                    $amount = floatval($_POST["withdrawAmount"]);
                    if ($amount > 0) {
                        if ($amount <= $_SESSION["balance"]) {
                            $_SESSION["balance"] -= $amount;
                            $transaction = date("Y-m-d H:i:s") . " - Withdrawal: $" . number_format($amount, 2);
                            $_SESSION["transactions"][] = $transaction;
                            $message = "Successfully withdrew $" . number_format($amount, 2) . ". New balance: $" . number_format($_SESSION["balance"], 2);
                        } else {
                            $message = "Insufficient funds. Your current balance is $" . number_format($_SESSION["balance"], 2);
                        }
                    } else {
                        $message = "Withdrawal amount must be greater than $0.00";
                    }
                }
                // Show Transactions
                elseif (isset($_POST["tranAction"])) {
                    if (empty($_SESSION["transactions"])) {
                        $message = "No transactions found for this session.";
                    } else {
                        $message = "Transaction History:<br>";
                        foreach ($_SESSION["transactions"] as $transaction) {
                            $message .= $transaction . "<br>";
                        }
                    }
                }
                // Change Password
                elseif (isset($_POST["changePass"])) {
                    $oldPass = $_POST["oldPassword"];
                    $newPass = $_POST["newPassword"];
                    $retypePass = $_POST["retypePassword"];

                    if ($oldPass == $_SESSION["password"]) {
                        if ($newPass == $retypePass && !empty($newPass)) {
                            $_SESSION["password"] = $newPass;
                            $message = "Password successfully changed!";
                        } else {
                            $message = "New passwords do not match or are empty!";
                        }
                    } else {
                        $message = "Old password is incorrect!";
                    }
                }
                // Logout
                elseif (isset($_POST["logout"])) {
                    session_destroy();
                    echo "<div style='background-color:lightgreen; border:green solid 1px; width:75%; margin:auto; text-align:center;'>";
                    echo "You have been successfully logged out!<br>";
                    echo "<a href='Project3.php'>Back to Login</a>";
                    echo "</div>";
                    exit();
                }
            }
            echo "<div style='background-color:pink; border:red solid 1px; width:75%; margin:auto;'>";
            echo "Welcome ".$user."<br>";
            date_default_timezone_set("America/New_York");
            $now = time();
            echo "Now is ".date("F j, Y", $now)." ".date("h:i:s A", $now)." <br>";
            echo "</div>";
            echo "<hr>";

            // Display message if any
            if (!empty($message)) {
                echo "<div style='background-color:lightyellow; border:orange solid 1px; width:75%; margin:auto; padding:10px;'>";
                echo $message;
                echo "</div>";
                echo "<hr>";
            }

            echo "<div style='background-color:lightblue; border:red solid 1px; width:75%; margin:auto;'>";
            echo "Choose what you want to do based on the following menu: <br>";
            echo "<hr>";

            echo "<form method='post' action='".$_SERVER['PHP_SELF']."'>";
            echo "<input type='radio' name='showBalance' value='1' required> Show Balance <br>";

            echo "<input type='radio' name='deposit' value='1' required> Deposit this amount: ";
            echo "<input type='text' name='depositAmount' placeholder='0.00'> <br>";

            echo "<input type='radio' name='withdraw' value='1' required> Withdraw this amount: ";
            echo "<input type='text' name='withdrawAmount' placeholder='0.00'> <br>";

            echo "<input type='radio' name='tranAction' value='1' required> Show my transactions <br>";

            echo "<input type='radio' name='changePass' value='1' required> Change my password <br>";
            /*
            echo "<div style='margin-left:20px;'>";
            echo "Old Password: <input type='password' name='oldPassword'><br>";
            echo "New Password: <input type='password' name='newPassword'><br>";
            echo "Re-type New Password: <input type='password' name='retypePassword'><br>";
            echo "</div><br>";
            */
            echo "<input type='radio' name='logout' value='1' required> Log Out <br>";
            echo "<hr>";
            
            echo "<input type='submit' name='submit' value='Submit'>";
            echo "</form>";
            echo "</div>";
        ?>
    </body>
</html>