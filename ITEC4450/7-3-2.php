<html>
    <head>
        <title>7-3-2</title>
    </head>

    <body>
        <?php
            require_once("My-DB-Functions.php");

            $conn = My_Connect_DB();

            echo "Show all Students: <br>";
            $sql = "SELECT * FROM Students;";
            Run_SQL_Show_Result($conn, $sql, "Students");
            echo "<br>";

            $_DEBUG_FIRST_TIME = 0;
            if ($_DEBUG_FIRST_TIME)
            {
                /*
                echo "Add a Student: <br>";
                $sql = "INSERT INTO Students(firstname, lastname, email)
                        VALUES('<PERSON>che<PERSON>', '<PERSON>', '<EMAIL>')";
                $sql = "INSERT INTO Students(firstname, lastname, email)
                        VALUES('<PERSON>', '<PERSON>', '<EMAIL>')";
                Run_SQL_Show_Result($conn, $sql, "Students");
                echo "<br>";
                */

                echo "Add age info to all students: <br>";
                $sql = "ALTER TABLE Students ADD age INT(3);";
                Run_SQL_Show_Result($conn, $sql, "Students");
                echo "<br>";

                echo "Remove all students' reg_date info: <br>";
                $sql = "ALTER TABLE Students DROP COLUMN reg_date;";
                Run_SQL_Show_Result($conn, $sql, "Students");
                echo "<br>";
            }

            echo "Change all students' age to 21: <br>";
            $sql = "UPDATE Students SET age=21;";
            Run_SQL_Show_Result($conn, $sql, "Students");
            echo "<br>";

            echo "Change a few students' age to be 19: <br>";
            $sql = "UPDATE Students SET age=19 WHERE id=16 OR id=18;"; // WHERE id=6 OR id=8
            Run_SQL_Show_Result($conn, $sql, "Students");
            echo "<br>";

            echo "Change all students' age to a random number between (1, 100): <br>";
            $sql = "UPDATE Students SET age=FLOOR(RAND()*99+1) WHERE age=21;";
            Run_SQL_Show_Result($conn, $sql, "Students");
            echo "<br>";

            echo "Find the total number of students: <br>";
            $sql = "SELECT COUNT(*) AS 'Total # of Students' FROM Students;";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Find the total number of students who are 40+ years old: <br>";
            $sql = "SELECT COUNT(*) AS 'Total # of Students who are 40+ years old' FROM Students WHERE age>=40;";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Find the average age of all Students: <br>";
            $sql = "SELECT AVG(age) AS 'Average age of all Students' FROM Students;";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Find the youngest and oldest Students: <br>";
            $sql = "SELECT MAX(age) AS 'Oldest age of all Students', MIN(age) AS 'Youngest age of all Students' FROM Students;";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Sort in ascending order by lastname for all Students: <br>";
            $sql = "SELECT * FROM Students ORDER BY lastname ASC;";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Sort in descending order by lastname for students whose firstname is Matilda: <br>";
            $sql = "SELECT * FROM Students WHERE firstname='Matilda' ORDER BY lastname DESC;";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Find how many Matilda are there: <br>";
            $sql = "SELECT COUNT(*) AS '# of students whose name is Matilda' FROM Students WHERE firstname='Matilda';";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Find how many people have the same name and sort them in descending order by number of people: <br>";
            $sql = "SELECT firstname, COUNT(firstname) AS 'No_of_Students_with_this_name' FROM Students GROUP BY firstname ORDER BY No_of_Students_with_this_name DESC;";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Find the youngest Students: <br>";
            $sql = "SELECT id, firstname, lastname, age FROM Students WHERE age=(SELECT MIN(age) FROM Students);";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Find the oldest Students: <br>";
            $sql = "SELECT id, firstname, lastname, age FROM Students WHERE age=(SELECT MAX(age) FROM Students);";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            echo "Find all the students who are older than the average age: <br>";
            $sql = "SELECT id, firstname, lastname, age FROM Students WHERE age>(SELECT AVG(age) FROM Students)";
            Run_Select_Show_Results($conn, $sql);
            echo "<br>";

            My_Disconnect_DB($conn);
        ?>
    </body>
</html>